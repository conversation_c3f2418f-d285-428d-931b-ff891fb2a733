{"version": 3, "names": ["_assert", "require", "_core", "_helperModuleImports", "_rewriteThis", "_rewriteLiveReferences", "_normalizeAndLoadMetadata", "Lazy", "_dynamicImport", "_getModuleName", "exports", "getDynamicImportSource", "rewriteModuleStatementsAndPrepareHeader", "path", "exportName", "strict", "allowTopLevelThis", "strictMode", "noInterop", "importInterop", "lazy", "getWrapperPayload", "toGetWrapperPayload", "wrapReference", "esNamespaceOnly", "filename", "constantReexports", "arguments", "loose", "enumerableModuleMeta", "noIncompleteNsImportDetection", "validateImportInteropOption", "assert", "isModule", "node", "sourceType", "meta", "normalizeModuleAndLoadMetadata", "initializeReexports", "rewriteThis", "rewriteLiveReferences", "hasStrict", "directives", "some", "directive", "value", "unshiftContainer", "t", "directiveLiteral", "headers", "hasExports", "push", "buildESModuleHeader", "nameList", "buildExportNameListDeclaration", "exportNameListName", "name", "statement", "buildExportInitializationStatements", "ensureStatementsHoisted", "statements", "for<PERSON>ach", "header", "_blockHoist", "wrapInterop", "programPath", "expr", "type", "callExpression", "hub", "addHelper", "booleanLiteral", "helper", "Error", "buildNamespaceInitStatements", "metadata", "sourceMetadata", "_wrapReference", "srcNamespaceId", "identifier", "localName", "importsNamespace", "template", "NAME", "SOURCE", "cloneNode", "srcNamespace", "wrap", "buildReexportsFromMeta", "reexportNamespace", "isIdentifier", "EXPORTS", "NAMESPACE", "reexportAll", "buildNamespaceReexport", "loc", "ReexportTemplate", "constant", "namespaceImport", "ast", "constantComputed", "spec", "_wrapReference2", "namespace", "stringSpecifiers", "Array", "from", "reexports", "importName", "interop", "has", "memberExpression", "stringLiteral", "astNodes", "VERIFY_NAME_LIST", "EXPORTS_LIST", "exportedVars", "Object", "create", "data", "local", "values", "names", "hasReexport", "source", "keys", "length", "scope", "generateUidIdentifier", "default", "variableDeclaration", "variableDeclarator", "valueToNode", "initStatements", "kind", "buildInitStatement", "reexportsStatements", "i", "sort", "a", "b", "results", "initStatement", "chunkSize", "uninitializedExportNames", "j", "buildUndefinedNode", "InitTemplate", "computed", "expression", "define", "exportNames", "initExpr", "expressionStatement", "reduce", "params"], "sources": ["../src/index.ts"], "sourcesContent": ["import assert from \"node:assert\";\nimport { template, types as t } from \"@babel/core\";\n\nimport { isModule } from \"@babel/helper-module-imports\";\n\nimport rewriteThis from \"./rewrite-this.ts\";\nimport rewriteLiveReferences from \"./rewrite-live-references.ts\";\nimport normalizeModuleAndLoadMetadata, {\n  hasExports,\n  isSideEffectImport,\n  validateImportInteropOption,\n} from \"./normalize-and-load-metadata.ts\";\nimport type {\n  ImportInterop,\n  InteropType,\n  ModuleMetadata,\n  SourceModuleMetadata,\n} from \"./normalize-and-load-metadata.ts\";\nimport * as Lazy from \"./lazy-modules.ts\";\nimport type { NodePath } from \"@babel/core\";\n\nexport { buildDynamicImport } from \"./dynamic-import.ts\";\n\nif (!process.env.BABEL_8_BREAKING && !USE_ESM && !IS_STANDALONE) {\n  // eslint-disable-next-line no-restricted-globals\n  exports.getDynamicImportSource =\n    // eslint-disable-next-line no-restricted-globals, import/extensions\n    require(\"./dynamic-import\").getDynamicImportSource;\n}\n\nexport { default as getModuleName } from \"./get-module-name.ts\";\nexport type { PluginOptions } from \"./get-module-name.ts\";\n\nexport { hasExports, isSideEffectImport, isModule, rewriteThis };\n\nexport interface RewriteModuleStatementsAndPrepareHeaderOptions {\n  exportName?: string;\n  strict: boolean;\n  allowTopLevelThis?: boolean;\n  strictMode: boolean;\n  loose?: boolean;\n  importInterop?: ImportInterop;\n  noInterop?: boolean;\n  lazy?: Lazy.Lazy;\n  getWrapperPayload?: (\n    source: string,\n    metadata: SourceModuleMetadata,\n    importNodes: t.Node[],\n  ) => unknown;\n  wrapReference?: (ref: t.Expression, payload: unknown) => t.Expression | null;\n  esNamespaceOnly?: boolean;\n  filename: string | undefined;\n  constantReexports?: boolean | void;\n  enumerableModuleMeta?: boolean | void;\n  noIncompleteNsImportDetection?: boolean | void;\n}\n\n/**\n * Perform all of the generic ES6 module rewriting needed to handle initial\n * module processing. This function will rewrite the majority of the given\n * program to reference the modules described by the returned metadata,\n * and returns a list of statements for use when initializing the module.\n */\nexport function rewriteModuleStatementsAndPrepareHeader(\n  path: NodePath<t.Program>,\n  {\n    exportName,\n    strict,\n    allowTopLevelThis,\n    strictMode,\n    noInterop,\n    importInterop = noInterop ? \"none\" : \"babel\",\n    // TODO(Babel 8): After that `lazy` implementation is moved to the CJS\n    // transform, remove this parameter.\n    lazy,\n    getWrapperPayload = Lazy.toGetWrapperPayload(lazy ?? false),\n    wrapReference = Lazy.wrapReference,\n    esNamespaceOnly,\n    filename,\n\n    constantReexports = process.env.BABEL_8_BREAKING\n      ? undefined\n      : arguments[1].loose,\n    enumerableModuleMeta = process.env.BABEL_8_BREAKING\n      ? undefined\n      : arguments[1].loose,\n    noIncompleteNsImportDetection,\n  }: RewriteModuleStatementsAndPrepareHeaderOptions,\n) {\n  validateImportInteropOption(importInterop);\n  assert(isModule(path), \"Cannot process module statements in a script\");\n  path.node.sourceType = \"script\";\n\n  const meta = normalizeModuleAndLoadMetadata(path, exportName, {\n    importInterop,\n    initializeReexports: constantReexports,\n    getWrapperPayload,\n    esNamespaceOnly,\n    filename,\n  });\n\n  if (!allowTopLevelThis) {\n    rewriteThis(path);\n  }\n\n  rewriteLiveReferences(path, meta, wrapReference);\n\n  if (strictMode !== false) {\n    const hasStrict = path.node.directives.some(directive => {\n      return directive.value.value === \"use strict\";\n    });\n    if (!hasStrict) {\n      path.unshiftContainer(\n        \"directives\",\n        t.directive(t.directiveLiteral(\"use strict\")),\n      );\n    }\n  }\n\n  const headers = [];\n  if (hasExports(meta) && !strict) {\n    headers.push(buildESModuleHeader(meta, enumerableModuleMeta));\n  }\n\n  const nameList = buildExportNameListDeclaration(path, meta);\n\n  if (nameList) {\n    meta.exportNameListName = nameList.name;\n    headers.push(nameList.statement);\n  }\n\n  // Create all of the statically known named exports.\n  headers.push(\n    ...buildExportInitializationStatements(\n      path,\n      meta,\n      wrapReference,\n      constantReexports,\n      noIncompleteNsImportDetection,\n    ),\n  );\n\n  return { meta, headers };\n}\n\n/**\n * Flag a set of statements as hoisted above all else so that module init\n * statements all run before user code.\n */\nexport function ensureStatementsHoisted(statements: t.Statement[]) {\n  // Force all of the header fields to be at the top of the file.\n  statements.forEach(header => {\n    // @ts-expect-error Fixme: handle _blockHoist property\n    header._blockHoist = 3;\n  });\n}\n\n/**\n * Given an expression for a standard import object, like \"require('foo')\",\n * wrap it in a call to the interop helpers based on the type.\n */\nexport function wrapInterop(\n  programPath: NodePath<t.Program>,\n  expr: t.Expression,\n  type: InteropType,\n): t.CallExpression {\n  if (type === \"none\") {\n    return null;\n  }\n\n  if (type === \"node-namespace\") {\n    return t.callExpression(\n      programPath.hub.addHelper(\"interopRequireWildcard\"),\n      [expr, t.booleanLiteral(true)],\n    );\n  } else if (type === \"node-default\") {\n    return null;\n  }\n\n  let helper;\n  if (type === \"default\") {\n    helper = \"interopRequireDefault\";\n  } else if (type === \"namespace\") {\n    helper = \"interopRequireWildcard\";\n  } else {\n    throw new Error(`Unknown interop: ${type}`);\n  }\n\n  return t.callExpression(programPath.hub.addHelper(helper), [expr]);\n}\n\n/**\n * Create the runtime initialization statements for a given requested source.\n * These will initialize all of the runtime import/export logic that\n * can't be handled statically by the statements created by\n * buildExportInitializationStatements().\n */\nexport function buildNamespaceInitStatements(\n  metadata: ModuleMetadata,\n  sourceMetadata: SourceModuleMetadata,\n  constantReexports: boolean | void = false,\n  wrapReference: (\n    ref: t.Identifier,\n    payload: unknown,\n  ) => t.Expression | null = Lazy.wrapReference,\n) {\n  const statements = [];\n\n  const srcNamespaceId = t.identifier(sourceMetadata.name);\n\n  for (const localName of sourceMetadata.importsNamespace) {\n    if (localName === sourceMetadata.name) continue;\n\n    // Create and assign binding to namespace object\n    statements.push(\n      template.statement`var NAME = SOURCE;`({\n        NAME: localName,\n        SOURCE: t.cloneNode(srcNamespaceId),\n      }),\n    );\n  }\n\n  const srcNamespace =\n    wrapReference(srcNamespaceId, sourceMetadata.wrap) ?? srcNamespaceId;\n\n  if (constantReexports) {\n    statements.push(\n      ...buildReexportsFromMeta(metadata, sourceMetadata, true, wrapReference),\n    );\n  }\n  for (const exportName of sourceMetadata.reexportNamespace) {\n    // Assign export to namespace object.\n    statements.push(\n      (!t.isIdentifier(srcNamespace)\n        ? template.statement`\n            Object.defineProperty(EXPORTS, \"NAME\", {\n              enumerable: true,\n              get: function() {\n                return NAMESPACE;\n              }\n            });\n          `\n        : template.statement`EXPORTS.NAME = NAMESPACE;`)({\n        EXPORTS: metadata.exportName,\n        NAME: exportName,\n        NAMESPACE: t.cloneNode(srcNamespace),\n      }),\n    );\n  }\n  if (sourceMetadata.reexportAll) {\n    const statement = buildNamespaceReexport(\n      metadata,\n      t.cloneNode(srcNamespace),\n      constantReexports,\n    );\n    statement.loc = sourceMetadata.reexportAll.loc;\n\n    // Iterate props creating getter for each prop.\n    statements.push(statement);\n  }\n  return statements;\n}\n\ninterface ReexportParts {\n  exports: string;\n  exportName: string;\n  namespaceImport: t.Expression;\n}\n\nconst ReexportTemplate = {\n  constant: ({ exports, exportName, namespaceImport }: ReexportParts) =>\n    template.statement.ast`\n      ${exports}.${exportName} = ${namespaceImport};\n    `,\n  constantComputed: ({ exports, exportName, namespaceImport }: ReexportParts) =>\n    template.statement.ast`\n      ${exports}[\"${exportName}\"] = ${namespaceImport};\n    `,\n  spec: ({ exports, exportName, namespaceImport }: ReexportParts) =>\n    template.statement.ast`\n      Object.defineProperty(${exports}, \"${exportName}\", {\n        enumerable: true,\n        get: function() {\n          return ${namespaceImport};\n        },\n      });\n    `,\n};\n\nfunction buildReexportsFromMeta(\n  meta: ModuleMetadata,\n  metadata: SourceModuleMetadata,\n  constantReexports: boolean,\n  wrapReference: (ref: t.Expression, payload: unknown) => t.Expression | null,\n): t.Statement[] {\n  let namespace: t.Expression = t.identifier(metadata.name);\n  namespace = wrapReference(namespace, metadata.wrap) ?? namespace;\n\n  const { stringSpecifiers } = meta;\n  return Array.from(metadata.reexports, ([exportName, importName]) => {\n    let namespaceImport: t.Expression = t.cloneNode(namespace);\n    if (importName === \"default\" && metadata.interop === \"node-default\") {\n      // Nothing, it's ok as-is\n    } else if (stringSpecifiers.has(importName)) {\n      namespaceImport = t.memberExpression(\n        namespaceImport,\n        t.stringLiteral(importName),\n        true,\n      );\n    } else {\n      namespaceImport = t.memberExpression(\n        namespaceImport,\n        t.identifier(importName),\n      );\n    }\n    const astNodes: ReexportParts = {\n      exports: meta.exportName,\n      exportName,\n      namespaceImport,\n    };\n    if (constantReexports || t.isIdentifier(namespaceImport)) {\n      if (stringSpecifiers.has(exportName)) {\n        return ReexportTemplate.constantComputed(astNodes);\n      } else {\n        return ReexportTemplate.constant(astNodes);\n      }\n    } else {\n      return ReexportTemplate.spec(astNodes);\n    }\n  });\n}\n\n/**\n * Build an \"__esModule\" header statement setting the property on a given object.\n */\nfunction buildESModuleHeader(\n  metadata: ModuleMetadata,\n  enumerableModuleMeta: boolean | void = false,\n) {\n  return (\n    enumerableModuleMeta\n      ? template.statement`\n        EXPORTS.__esModule = true;\n      `\n      : template.statement`\n        Object.defineProperty(EXPORTS, \"__esModule\", {\n          value: true,\n        });\n      `\n  )({ EXPORTS: metadata.exportName });\n}\n\n/**\n * Create a re-export initialization loop for a specific imported namespace.\n */\nfunction buildNamespaceReexport(\n  metadata: ModuleMetadata,\n  namespace: t.Expression,\n  constantReexports: boolean | void,\n) {\n  return (\n    constantReexports\n      ? template.statement`\n        Object.keys(NAMESPACE).forEach(function(key) {\n          if (key === \"default\" || key === \"__esModule\") return;\n          VERIFY_NAME_LIST;\n          if (key in EXPORTS && EXPORTS[key] === NAMESPACE[key]) return;\n\n          EXPORTS[key] = NAMESPACE[key];\n        });\n      `\n      : // Also skip already assigned bindings if they are strictly equal\n        // to be somewhat more spec-compliant when a file has multiple\n        // namespace re-exports that would cause a binding to be exported\n        // multiple times. However, multiple bindings of the same name that\n        // export the same primitive value are silently skipped\n        // (the spec requires an \"ambiguous bindings\" early error here).\n        template.statement`\n        Object.keys(NAMESPACE).forEach(function(key) {\n          if (key === \"default\" || key === \"__esModule\") return;\n          VERIFY_NAME_LIST;\n          if (key in EXPORTS && EXPORTS[key] === NAMESPACE[key]) return;\n\n          Object.defineProperty(EXPORTS, key, {\n            enumerable: true,\n            get: function() {\n              return NAMESPACE[key];\n            },\n          });\n        });\n    `\n  )({\n    NAMESPACE: namespace,\n    EXPORTS: metadata.exportName,\n    VERIFY_NAME_LIST: metadata.exportNameListName\n      ? template`\n            if (Object.prototype.hasOwnProperty.call(EXPORTS_LIST, key)) return;\n          `({ EXPORTS_LIST: metadata.exportNameListName })\n      : null,\n  });\n}\n\n/**\n * Build a statement declaring a variable that contains all of the exported\n * variable names in an object so they can easily be referenced from an\n * export * from statement to check for conflicts.\n */\nfunction buildExportNameListDeclaration(\n  programPath: NodePath,\n  metadata: ModuleMetadata,\n) {\n  const exportedVars = Object.create(null);\n  for (const data of metadata.local.values()) {\n    for (const name of data.names) {\n      exportedVars[name] = true;\n    }\n  }\n\n  let hasReexport = false;\n  for (const data of metadata.source.values()) {\n    for (const exportName of data.reexports.keys()) {\n      exportedVars[exportName] = true;\n    }\n    for (const exportName of data.reexportNamespace) {\n      exportedVars[exportName] = true;\n    }\n\n    hasReexport = hasReexport || !!data.reexportAll;\n  }\n\n  if (!hasReexport || Object.keys(exportedVars).length === 0) return null;\n\n  const name = programPath.scope.generateUidIdentifier(\"exportNames\");\n\n  delete exportedVars.default;\n\n  return {\n    name: name.name,\n    statement: t.variableDeclaration(\"var\", [\n      t.variableDeclarator(name, t.valueToNode(exportedVars)),\n    ]),\n  };\n}\n\n/**\n * Create a set of statements that will initialize all of the statically-known\n * export names with their expected values.\n */\nfunction buildExportInitializationStatements(\n  programPath: NodePath,\n  metadata: ModuleMetadata,\n  wrapReference: (ref: t.Expression, payload: unknown) => t.Expression | null,\n  constantReexports: boolean | void = false,\n  noIncompleteNsImportDetection: boolean | void = false,\n) {\n  const initStatements: Array<[string, t.Statement | null]> = [];\n\n  for (const [localName, data] of metadata.local) {\n    if (data.kind === \"import\") {\n      // No-open since these are explicitly set with the \"reexports\" block.\n    } else if (data.kind === \"hoisted\") {\n      initStatements.push([\n        // data.names is always of length 1 because a hoisted export\n        // name must be id of a function declaration\n        data.names[0],\n        buildInitStatement(metadata, data.names, t.identifier(localName)),\n      ]);\n    } else if (!noIncompleteNsImportDetection) {\n      for (const exportName of data.names) {\n        initStatements.push([exportName, null]);\n      }\n    }\n  }\n\n  for (const data of metadata.source.values()) {\n    if (!constantReexports) {\n      const reexportsStatements = buildReexportsFromMeta(\n        metadata,\n        data,\n        false,\n        wrapReference,\n      );\n      const reexports = [...data.reexports.keys()];\n      for (let i = 0; i < reexportsStatements.length; i++) {\n        initStatements.push([reexports[i], reexportsStatements[i]]);\n      }\n    }\n    if (!noIncompleteNsImportDetection) {\n      for (const exportName of data.reexportNamespace) {\n        initStatements.push([exportName, null]);\n      }\n    }\n  }\n\n  // https://tc39.es/ecma262/#sec-module-namespace-exotic-objects\n  // The [Exports] list is ordered as if an Array of those String values\n  // had been sorted using %Array.prototype.sort% using undefined as comparefn\n  initStatements.sort(([a], [b]) => {\n    if (a < b) return -1;\n    if (b < a) return 1;\n    return 0;\n  });\n\n  const results = [];\n  if (noIncompleteNsImportDetection) {\n    for (const [, initStatement] of initStatements) {\n      results.push(initStatement);\n    }\n  } else {\n    // We generate init statements (`exports.a = exports.b = ... = void 0`)\n    // for every 100 exported names to avoid deeply-nested AST structures.\n    const chunkSize = 100;\n    for (let i = 0; i < initStatements.length; i += chunkSize) {\n      let uninitializedExportNames = [];\n      for (let j = 0; j < chunkSize && i + j < initStatements.length; j++) {\n        const [exportName, initStatement] = initStatements[i + j];\n        if (initStatement !== null) {\n          if (uninitializedExportNames.length > 0) {\n            results.push(\n              buildInitStatement(\n                metadata,\n                uninitializedExportNames,\n                programPath.scope.buildUndefinedNode(),\n              ),\n            );\n            // reset after uninitializedExportNames has been transformed\n            // to init statements\n            uninitializedExportNames = [];\n          }\n          results.push(initStatement);\n        } else {\n          uninitializedExportNames.push(exportName);\n        }\n      }\n      if (uninitializedExportNames.length > 0) {\n        results.push(\n          buildInitStatement(\n            metadata,\n            uninitializedExportNames,\n            programPath.scope.buildUndefinedNode(),\n          ),\n        );\n      }\n    }\n  }\n\n  return results;\n}\n\ninterface InitParts {\n  exports: string;\n  name: string;\n  value: t.Expression;\n}\n\n/**\n * Given a set of export names, create a set of nested assignments to\n * initialize them all to a given expression.\n */\nconst InitTemplate = {\n  computed: ({ exports, name, value }: InitParts) =>\n    template.expression.ast`${exports}[\"${name}\"] = ${value}`,\n  default: ({ exports, name, value }: InitParts) =>\n    template.expression.ast`${exports}.${name} = ${value}`,\n  define: ({ exports, name, value }: InitParts) =>\n    template.expression.ast`\n      Object.defineProperty(${exports}, \"${name}\", {\n        enumerable: true,\n        value: void 0,\n        writable: true\n      })[\"${name}\"] = ${value}`,\n};\n\nfunction buildInitStatement(\n  metadata: ModuleMetadata,\n  exportNames: string[],\n  initExpr: t.Expression,\n) {\n  const { stringSpecifiers, exportName: exports } = metadata;\n  return t.expressionStatement(\n    exportNames.reduce((value, name) => {\n      const params = {\n        exports,\n        name,\n        value,\n      };\n\n      if (name === \"__proto__\") {\n        return InitTemplate.define(params);\n      }\n\n      if (stringSpecifiers.has(name)) {\n        return InitTemplate.computed(params);\n      }\n\n      return InitTemplate.default(params);\n    }, initExpr),\n  );\n}\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA,IAAAA,OAAA,GAAAC,OAAA;AACA,IAAAC,KAAA,GAAAD,OAAA;AAEA,IAAAE,oBAAA,GAAAF,OAAA;AAEA,IAAAG,YAAA,GAAAH,OAAA;AACA,IAAAI,sBAAA,GAAAJ,OAAA;AACA,IAAAK,yBAAA,GAAAL,OAAA;AAWA,IAAAM,IAAA,GAAAN,OAAA;AAGA,IAAAO,cAAA,GAAAP,OAAA;AASA,IAAAQ,cAAA,GAAAR,OAAA;AAPiE;EAE/DS,OAAO,CAACC,sBAAsB,GAE5BV,OAAO,CAAC,kBAAkB,CAAC,CAACU,sBAAsB;AACtD;AAmCO,SAASC,uCAAuCA,CACrDC,IAAyB,EACzB;EACEC,UAAU;EACVC,MAAM;EACNC,iBAAiB;EACjBC,UAAU;EACVC,SAAS;EACTC,aAAa,GAAGD,SAAS,GAAG,MAAM,GAAG,OAAO;EAG5CE,IAAI;EACJC,iBAAiB,GAAGd,IAAI,CAACe,mBAAmB,CAACF,IAAI,WAAJA,IAAI,GAAI,KAAK,CAAC;EAC3DG,aAAa,GAAGhB,IAAI,CAACgB,aAAa;EAClCC,eAAe;EACfC,QAAQ;EAERC,iBAAiB,GAEbC,SAAS,CAAC,CAAC,CAAC,CAACC,KAAK;EACtBC,oBAAoB,GAEhBF,SAAS,CAAC,CAAC,CAAC,CAACC,KAAK;EACtBE;AAC8C,CAAC,EACjD;EACA,IAAAC,qDAA2B,EAACZ,aAAa,CAAC;EAC1Ca,OAAM,CAAC,IAAAC,6BAAQ,EAACpB,IAAI,CAAC,EAAE,8CAA8C,CAAC;EACtEA,IAAI,CAACqB,IAAI,CAACC,UAAU,GAAG,QAAQ;EAE/B,MAAMC,IAAI,GAAG,IAAAC,iCAA8B,EAACxB,IAAI,EAAEC,UAAU,EAAE;IAC5DK,aAAa;IACbmB,mBAAmB,EAAEZ,iBAAiB;IACtCL,iBAAiB;IACjBG,eAAe;IACfC;EACF,CAAC,CAAC;EAEF,IAAI,CAACT,iBAAiB,EAAE;IACtB,IAAAuB,oBAAW,EAAC1B,IAAI,CAAC;EACnB;EAEA,IAAA2B,8BAAqB,EAAC3B,IAAI,EAAEuB,IAAI,EAAEb,aAAa,CAAC;EAEhD,IAAIN,UAAU,KAAK,KAAK,EAAE;IACxB,MAAMwB,SAAS,GAAG5B,IAAI,CAACqB,IAAI,CAACQ,UAAU,CAACC,IAAI,CAACC,SAAS,IAAI;MACvD,OAAOA,SAAS,CAACC,KAAK,CAACA,KAAK,KAAK,YAAY;IAC/C,CAAC,CAAC;IACF,IAAI,CAACJ,SAAS,EAAE;MACd5B,IAAI,CAACiC,gBAAgB,CACnB,YAAY,EACZC,WAAC,CAACH,SAAS,CAACG,WAAC,CAACC,gBAAgB,CAAC,YAAY,CAAC,CAC9C,CAAC;IACH;EACF;EAEA,MAAMC,OAAO,GAAG,EAAE;EAClB,IAAI,IAAAC,oCAAU,EAACd,IAAI,CAAC,IAAI,CAACrB,MAAM,EAAE;IAC/BkC,OAAO,CAACE,IAAI,CAACC,mBAAmB,CAAChB,IAAI,EAAEP,oBAAoB,CAAC,CAAC;EAC/D;EAEA,MAAMwB,QAAQ,GAAGC,8BAA8B,CAACzC,IAAI,EAAEuB,IAAI,CAAC;EAE3D,IAAIiB,QAAQ,EAAE;IACZjB,IAAI,CAACmB,kBAAkB,GAAGF,QAAQ,CAACG,IAAI;IACvCP,OAAO,CAACE,IAAI,CAACE,QAAQ,CAACI,SAAS,CAAC;EAClC;EAGAR,OAAO,CAACE,IAAI,CACV,GAAGO,mCAAmC,CACpC7C,IAAI,EACJuB,IAAI,EACJb,aAAa,EACbG,iBAAiB,EACjBI,6BACF,CACF,CAAC;EAED,OAAO;IAAEM,IAAI;IAAEa;EAAQ,CAAC;AAC1B;AAMO,SAASU,uBAAuBA,CAACC,UAAyB,EAAE;EAEjEA,UAAU,CAACC,OAAO,CAACC,MAAM,IAAI;IAE3BA,MAAM,CAACC,WAAW,GAAG,CAAC;EACxB,CAAC,CAAC;AACJ;AAMO,SAASC,WAAWA,CACzBC,WAAgC,EAChCC,IAAkB,EAClBC,IAAiB,EACC;EAClB,IAAIA,IAAI,KAAK,MAAM,EAAE;IACnB,OAAO,IAAI;EACb;EAEA,IAAIA,IAAI,KAAK,gBAAgB,EAAE;IAC7B,OAAOpB,WAAC,CAACqB,cAAc,CACrBH,WAAW,CAACI,GAAG,CAACC,SAAS,CAAC,wBAAwB,CAAC,EACnD,CAACJ,IAAI,EAAEnB,WAAC,CAACwB,cAAc,CAAC,IAAI,CAAC,CAC/B,CAAC;EACH,CAAC,MAAM,IAAIJ,IAAI,KAAK,cAAc,EAAE;IAClC,OAAO,IAAI;EACb;EAEA,IAAIK,MAAM;EACV,IAAIL,IAAI,KAAK,SAAS,EAAE;IACtBK,MAAM,GAAG,uBAAuB;EAClC,CAAC,MAAM,IAAIL,IAAI,KAAK,WAAW,EAAE;IAC/BK,MAAM,GAAG,wBAAwB;EACnC,CAAC,MAAM;IACL,MAAM,IAAIC,KAAK,CAAC,oBAAoBN,IAAI,EAAE,CAAC;EAC7C;EAEA,OAAOpB,WAAC,CAACqB,cAAc,CAACH,WAAW,CAACI,GAAG,CAACC,SAAS,CAACE,MAAM,CAAC,EAAE,CAACN,IAAI,CAAC,CAAC;AACpE;AAQO,SAASQ,4BAA4BA,CAC1CC,QAAwB,EACxBC,cAAoC,EACpClD,iBAAiC,GAAG,KAAK,EACzCH,aAGwB,GAAGhB,IAAI,CAACgB,aAAa,EAC7C;EAAA,IAAAsD,cAAA;EACA,MAAMjB,UAAU,GAAG,EAAE;EAErB,MAAMkB,cAAc,GAAG/B,WAAC,CAACgC,UAAU,CAACH,cAAc,CAACpB,IAAI,CAAC;EAExD,KAAK,MAAMwB,SAAS,IAAIJ,cAAc,CAACK,gBAAgB,EAAE;IACvD,IAAID,SAAS,KAAKJ,cAAc,CAACpB,IAAI,EAAE;IAGvCI,UAAU,CAACT,IAAI,CACb+B,cAAQ,CAACzB,SAAS,oBAAoB,CAAC;MACrC0B,IAAI,EAAEH,SAAS;MACfI,MAAM,EAAErC,WAAC,CAACsC,SAAS,CAACP,cAAc;IACpC,CAAC,CACH,CAAC;EACH;EAEA,MAAMQ,YAAY,IAAAT,cAAA,GAChBtD,aAAa,CAACuD,cAAc,EAAEF,cAAc,CAACW,IAAI,CAAC,YAAAV,cAAA,GAAIC,cAAc;EAEtE,IAAIpD,iBAAiB,EAAE;IACrBkC,UAAU,CAACT,IAAI,CACb,GAAGqC,sBAAsB,CAACb,QAAQ,EAAEC,cAAc,EAAE,IAAI,EAAErD,aAAa,CACzE,CAAC;EACH;EACA,KAAK,MAAMT,UAAU,IAAI8D,cAAc,CAACa,iBAAiB,EAAE;IAEzD7B,UAAU,CAACT,IAAI,CACb,CAAC,CAACJ,WAAC,CAAC2C,YAAY,CAACJ,YAAY,CAAC,GAC1BJ,cAAQ,CAACzB,SAAS;AAC5B;AACA;AACA;AACA;AACA;AACA;AACA,WAAW,GACDyB,cAAQ,CAACzB,SAAS,2BAA2B,EAAE;MACjDkC,OAAO,EAAEhB,QAAQ,CAAC7D,UAAU;MAC5BqE,IAAI,EAAErE,UAAU;MAChB8E,SAAS,EAAE7C,WAAC,CAACsC,SAAS,CAACC,YAAY;IACrC,CAAC,CACH,CAAC;EACH;EACA,IAAIV,cAAc,CAACiB,WAAW,EAAE;IAC9B,MAAMpC,SAAS,GAAGqC,sBAAsB,CACtCnB,QAAQ,EACR5B,WAAC,CAACsC,SAAS,CAACC,YAAY,CAAC,EACzB5D,iBACF,CAAC;IACD+B,SAAS,CAACsC,GAAG,GAAGnB,cAAc,CAACiB,WAAW,CAACE,GAAG;IAG9CnC,UAAU,CAACT,IAAI,CAACM,SAAS,CAAC;EAC5B;EACA,OAAOG,UAAU;AACnB;AAQA,MAAMoC,gBAAgB,GAAG;EACvBC,QAAQ,EAAEA,CAAC;IAAEvF,OAAO;IAAEI,UAAU;IAAEoF;EAA+B,CAAC,KAChEhB,cAAQ,CAACzB,SAAS,CAAC0C,GAAG;AAC1B,QAAQzF,OAAO,IAAII,UAAU,MAAMoF,eAAe;AAClD,KAAK;EACHE,gBAAgB,EAAEA,CAAC;IAAE1F,OAAO;IAAEI,UAAU;IAAEoF;EAA+B,CAAC,KACxEhB,cAAQ,CAACzB,SAAS,CAAC0C,GAAG;AAC1B,QAAQzF,OAAO,KAAKI,UAAU,QAAQoF,eAAe;AACrD,KAAK;EACHG,IAAI,EAAEA,CAAC;IAAE3F,OAAO;IAAEI,UAAU;IAAEoF;EAA+B,CAAC,KAC5DhB,cAAQ,CAACzB,SAAS,CAAC0C,GAAG;AAC1B,8BAA8BzF,OAAO,MAAMI,UAAU;AACrD;AACA;AACA,mBAAmBoF,eAAe;AAClC;AACA;AACA;AACA,CAAC;AAED,SAASV,sBAAsBA,CAC7BpD,IAAoB,EACpBuC,QAA8B,EAC9BjD,iBAA0B,EAC1BH,aAA2E,EAC5D;EAAA,IAAA+E,eAAA;EACf,IAAIC,SAAuB,GAAGxD,WAAC,CAACgC,UAAU,CAACJ,QAAQ,CAACnB,IAAI,CAAC;EACzD+C,SAAS,IAAAD,eAAA,GAAG/E,aAAa,CAACgF,SAAS,EAAE5B,QAAQ,CAACY,IAAI,CAAC,YAAAe,eAAA,GAAIC,SAAS;EAEhE,MAAM;IAAEC;EAAiB,CAAC,GAAGpE,IAAI;EACjC,OAAOqE,KAAK,CAACC,IAAI,CAAC/B,QAAQ,CAACgC,SAAS,EAAE,CAAC,CAAC7F,UAAU,EAAE8F,UAAU,CAAC,KAAK;IAClE,IAAIV,eAA6B,GAAGnD,WAAC,CAACsC,SAAS,CAACkB,SAAS,CAAC;IAC1D,IAAIK,UAAU,KAAK,SAAS,IAAIjC,QAAQ,CAACkC,OAAO,KAAK,cAAc,EAAE,CAErE,CAAC,MAAM,IAAIL,gBAAgB,CAACM,GAAG,CAACF,UAAU,CAAC,EAAE;MAC3CV,eAAe,GAAGnD,WAAC,CAACgE,gBAAgB,CAClCb,eAAe,EACfnD,WAAC,CAACiE,aAAa,CAACJ,UAAU,CAAC,EAC3B,IACF,CAAC;IACH,CAAC,MAAM;MACLV,eAAe,GAAGnD,WAAC,CAACgE,gBAAgB,CAClCb,eAAe,EACfnD,WAAC,CAACgC,UAAU,CAAC6B,UAAU,CACzB,CAAC;IACH;IACA,MAAMK,QAAuB,GAAG;MAC9BvG,OAAO,EAAE0B,IAAI,CAACtB,UAAU;MACxBA,UAAU;MACVoF;IACF,CAAC;IACD,IAAIxE,iBAAiB,IAAIqB,WAAC,CAAC2C,YAAY,CAACQ,eAAe,CAAC,EAAE;MACxD,IAAIM,gBAAgB,CAACM,GAAG,CAAChG,UAAU,CAAC,EAAE;QACpC,OAAOkF,gBAAgB,CAACI,gBAAgB,CAACa,QAAQ,CAAC;MACpD,CAAC,MAAM;QACL,OAAOjB,gBAAgB,CAACC,QAAQ,CAACgB,QAAQ,CAAC;MAC5C;IACF,CAAC,MAAM;MACL,OAAOjB,gBAAgB,CAACK,IAAI,CAACY,QAAQ,CAAC;IACxC;EACF,CAAC,CAAC;AACJ;AAKA,SAAS7D,mBAAmBA,CAC1BuB,QAAwB,EACxB9C,oBAAoC,GAAG,KAAK,EAC5C;EACA,OAAO,CACLA,oBAAoB,GAChBqD,cAAQ,CAACzB,SAAS;AAC1B;AACA,OAAO,GACCyB,cAAQ,CAACzB,SAAS;AAC1B;AACA;AACA;AACA,OAAO,EACH;IAAEkC,OAAO,EAAEhB,QAAQ,CAAC7D;EAAW,CAAC,CAAC;AACrC;AAKA,SAASgF,sBAAsBA,CAC7BnB,QAAwB,EACxB4B,SAAuB,EACvB7E,iBAAiC,EACjC;EACA,OAAO,CACLA,iBAAiB,GACbwD,cAAQ,CAACzB,SAAS;AAC1B;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,GAOCyB,cAAQ,CAACzB,SAAS;AAC1B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,KAAK,EACD;IACAmC,SAAS,EAAEW,SAAS;IACpBZ,OAAO,EAAEhB,QAAQ,CAAC7D,UAAU;IAC5BoG,gBAAgB,EAAEvC,QAAQ,CAACpB,kBAAkB,GACzC,IAAA2B,cAAQ;AAChB;AACA,WAAW,CAAC;MAAEiC,YAAY,EAAExC,QAAQ,CAACpB;IAAmB,CAAC,CAAC,GAClD;EACN,CAAC,CAAC;AACJ;AAOA,SAASD,8BAA8BA,CACrCW,WAAqB,EACrBU,QAAwB,EACxB;EACA,MAAMyC,YAAY,GAAGC,MAAM,CAACC,MAAM,CAAC,IAAI,CAAC;EACxC,KAAK,MAAMC,IAAI,IAAI5C,QAAQ,CAAC6C,KAAK,CAACC,MAAM,CAAC,CAAC,EAAE;IAC1C,KAAK,MAAMjE,IAAI,IAAI+D,IAAI,CAACG,KAAK,EAAE;MAC7BN,YAAY,CAAC5D,IAAI,CAAC,GAAG,IAAI;IAC3B;EACF;EAEA,IAAImE,WAAW,GAAG,KAAK;EACvB,KAAK,MAAMJ,IAAI,IAAI5C,QAAQ,CAACiD,MAAM,CAACH,MAAM,CAAC,CAAC,EAAE;IAC3C,KAAK,MAAM3G,UAAU,IAAIyG,IAAI,CAACZ,SAAS,CAACkB,IAAI,CAAC,CAAC,EAAE;MAC9CT,YAAY,CAACtG,UAAU,CAAC,GAAG,IAAI;IACjC;IACA,KAAK,MAAMA,UAAU,IAAIyG,IAAI,CAAC9B,iBAAiB,EAAE;MAC/C2B,YAAY,CAACtG,UAAU,CAAC,GAAG,IAAI;IACjC;IAEA6G,WAAW,GAAGA,WAAW,IAAI,CAAC,CAACJ,IAAI,CAAC1B,WAAW;EACjD;EAEA,IAAI,CAAC8B,WAAW,IAAIN,MAAM,CAACQ,IAAI,CAACT,YAAY,CAAC,CAACU,MAAM,KAAK,CAAC,EAAE,OAAO,IAAI;EAEvE,MAAMtE,IAAI,GAAGS,WAAW,CAAC8D,KAAK,CAACC,qBAAqB,CAAC,aAAa,CAAC;EAEnE,OAAOZ,YAAY,CAACa,OAAO;EAE3B,OAAO;IACLzE,IAAI,EAAEA,IAAI,CAACA,IAAI;IACfC,SAAS,EAAEV,WAAC,CAACmF,mBAAmB,CAAC,KAAK,EAAE,CACtCnF,WAAC,CAACoF,kBAAkB,CAAC3E,IAAI,EAAET,WAAC,CAACqF,WAAW,CAAChB,YAAY,CAAC,CAAC,CACxD;EACH,CAAC;AACH;AAMA,SAAS1D,mCAAmCA,CAC1CO,WAAqB,EACrBU,QAAwB,EACxBpD,aAA2E,EAC3EG,iBAAiC,GAAG,KAAK,EACzCI,6BAA6C,GAAG,KAAK,EACrD;EACA,MAAMuG,cAAmD,GAAG,EAAE;EAE9D,KAAK,MAAM,CAACrD,SAAS,EAAEuC,IAAI,CAAC,IAAI5C,QAAQ,CAAC6C,KAAK,EAAE;IAC9C,IAAID,IAAI,CAACe,IAAI,KAAK,QAAQ,EAAE,CAE5B,CAAC,MAAM,IAAIf,IAAI,CAACe,IAAI,KAAK,SAAS,EAAE;MAClCD,cAAc,CAAClF,IAAI,CAAC,CAGlBoE,IAAI,CAACG,KAAK,CAAC,CAAC,CAAC,EACba,kBAAkB,CAAC5D,QAAQ,EAAE4C,IAAI,CAACG,KAAK,EAAE3E,WAAC,CAACgC,UAAU,CAACC,SAAS,CAAC,CAAC,CAClE,CAAC;IACJ,CAAC,MAAM,IAAI,CAAClD,6BAA6B,EAAE;MACzC,KAAK,MAAMhB,UAAU,IAAIyG,IAAI,CAACG,KAAK,EAAE;QACnCW,cAAc,CAAClF,IAAI,CAAC,CAACrC,UAAU,EAAE,IAAI,CAAC,CAAC;MACzC;IACF;EACF;EAEA,KAAK,MAAMyG,IAAI,IAAI5C,QAAQ,CAACiD,MAAM,CAACH,MAAM,CAAC,CAAC,EAAE;IAC3C,IAAI,CAAC/F,iBAAiB,EAAE;MACtB,MAAM8G,mBAAmB,GAAGhD,sBAAsB,CAChDb,QAAQ,EACR4C,IAAI,EACJ,KAAK,EACLhG,aACF,CAAC;MACD,MAAMoF,SAAS,GAAG,CAAC,GAAGY,IAAI,CAACZ,SAAS,CAACkB,IAAI,CAAC,CAAC,CAAC;MAC5C,KAAK,IAAIY,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGD,mBAAmB,CAACV,MAAM,EAAEW,CAAC,EAAE,EAAE;QACnDJ,cAAc,CAAClF,IAAI,CAAC,CAACwD,SAAS,CAAC8B,CAAC,CAAC,EAAED,mBAAmB,CAACC,CAAC,CAAC,CAAC,CAAC;MAC7D;IACF;IACA,IAAI,CAAC3G,6BAA6B,EAAE;MAClC,KAAK,MAAMhB,UAAU,IAAIyG,IAAI,CAAC9B,iBAAiB,EAAE;QAC/C4C,cAAc,CAAClF,IAAI,CAAC,CAACrC,UAAU,EAAE,IAAI,CAAC,CAAC;MACzC;IACF;EACF;EAKAuH,cAAc,CAACK,IAAI,CAAC,CAAC,CAACC,CAAC,CAAC,EAAE,CAACC,CAAC,CAAC,KAAK;IAChC,IAAID,CAAC,GAAGC,CAAC,EAAE,OAAO,CAAC,CAAC;IACpB,IAAIA,CAAC,GAAGD,CAAC,EAAE,OAAO,CAAC;IACnB,OAAO,CAAC;EACV,CAAC,CAAC;EAEF,MAAME,OAAO,GAAG,EAAE;EAClB,IAAI/G,6BAA6B,EAAE;IACjC,KAAK,MAAM,GAAGgH,aAAa,CAAC,IAAIT,cAAc,EAAE;MAC9CQ,OAAO,CAAC1F,IAAI,CAAC2F,aAAa,CAAC;IAC7B;EACF,CAAC,MAAM;IAGL,MAAMC,SAAS,GAAG,GAAG;IACrB,KAAK,IAAIN,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGJ,cAAc,CAACP,MAAM,EAAEW,CAAC,IAAIM,SAAS,EAAE;MACzD,IAAIC,wBAAwB,GAAG,EAAE;MACjC,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGF,SAAS,IAAIN,CAAC,GAAGQ,CAAC,GAAGZ,cAAc,CAACP,MAAM,EAAEmB,CAAC,EAAE,EAAE;QACnE,MAAM,CAACnI,UAAU,EAAEgI,aAAa,CAAC,GAAGT,cAAc,CAACI,CAAC,GAAGQ,CAAC,CAAC;QACzD,IAAIH,aAAa,KAAK,IAAI,EAAE;UAC1B,IAAIE,wBAAwB,CAAClB,MAAM,GAAG,CAAC,EAAE;YACvCe,OAAO,CAAC1F,IAAI,CACVoF,kBAAkB,CAChB5D,QAAQ,EACRqE,wBAAwB,EACxB/E,WAAW,CAAC8D,KAAK,CAACmB,kBAAkB,CAAC,CACvC,CACF,CAAC;YAGDF,wBAAwB,GAAG,EAAE;UAC/B;UACAH,OAAO,CAAC1F,IAAI,CAAC2F,aAAa,CAAC;QAC7B,CAAC,MAAM;UACLE,wBAAwB,CAAC7F,IAAI,CAACrC,UAAU,CAAC;QAC3C;MACF;MACA,IAAIkI,wBAAwB,CAAClB,MAAM,GAAG,CAAC,EAAE;QACvCe,OAAO,CAAC1F,IAAI,CACVoF,kBAAkB,CAChB5D,QAAQ,EACRqE,wBAAwB,EACxB/E,WAAW,CAAC8D,KAAK,CAACmB,kBAAkB,CAAC,CACvC,CACF,CAAC;MACH;IACF;EACF;EAEA,OAAOL,OAAO;AAChB;AAYA,MAAMM,YAAY,GAAG;EACnBC,QAAQ,EAAEA,CAAC;IAAE1I,OAAO;IAAE8C,IAAI;IAAEX;EAAiB,CAAC,KAC5CqC,cAAQ,CAACmE,UAAU,CAAClD,GAAG,GAAGzF,OAAO,KAAK8C,IAAI,QAAQX,KAAK,EAAE;EAC3DoF,OAAO,EAAEA,CAAC;IAAEvH,OAAO;IAAE8C,IAAI;IAAEX;EAAiB,CAAC,KAC3CqC,cAAQ,CAACmE,UAAU,CAAClD,GAAG,GAAGzF,OAAO,IAAI8C,IAAI,MAAMX,KAAK,EAAE;EACxDyG,MAAM,EAAEA,CAAC;IAAE5I,OAAO;IAAE8C,IAAI;IAAEX;EAAiB,CAAC,KAC1CqC,cAAQ,CAACmE,UAAU,CAAClD,GAAG;AAC3B,8BAA8BzF,OAAO,MAAM8C,IAAI;AAC/C;AACA;AACA;AACA,YAAYA,IAAI,QAAQX,KAAK;AAC7B,CAAC;AAED,SAAS0F,kBAAkBA,CACzB5D,QAAwB,EACxB4E,WAAqB,EACrBC,QAAsB,EACtB;EACA,MAAM;IAAEhD,gBAAgB;IAAE1F,UAAU,EAAEJ;EAAQ,CAAC,GAAGiE,QAAQ;EAC1D,OAAO5B,WAAC,CAAC0G,mBAAmB,CAC1BF,WAAW,CAACG,MAAM,CAAC,CAAC7G,KAAK,EAAEW,IAAI,KAAK;IAClC,MAAMmG,MAAM,GAAG;MACbjJ,OAAO;MACP8C,IAAI;MACJX;IACF,CAAC;IAED,IAAIW,IAAI,KAAK,WAAW,EAAE;MACxB,OAAO2F,YAAY,CAACG,MAAM,CAACK,MAAM,CAAC;IACpC;IAEA,IAAInD,gBAAgB,CAACM,GAAG,CAACtD,IAAI,CAAC,EAAE;MAC9B,OAAO2F,YAAY,CAACC,QAAQ,CAACO,MAAM,CAAC;IACtC;IAEA,OAAOR,YAAY,CAAClB,OAAO,CAAC0B,MAAM,CAAC;EACrC,CAAC,EAAEH,QAAQ,CACb,CAAC;AACH", "ignoreList": []}