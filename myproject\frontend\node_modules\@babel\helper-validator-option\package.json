{"name": "@babel/helper-validator-option", "version": "7.27.1", "description": "Validate plugin/preset options", "repository": {"type": "git", "url": "https://github.com/babel/babel.git", "directory": "packages/babel-helper-validator-option"}, "license": "MIT", "publishConfig": {"access": "public"}, "main": "./lib/index.js", "exports": {".": {"types": "./lib/index.d.ts", "default": "./lib/index.js"}, "./package.json": "./package.json"}, "engines": {"node": ">=6.9.0"}, "author": "The Babel Team (https://babel.dev/team)", "type": "commonjs"}