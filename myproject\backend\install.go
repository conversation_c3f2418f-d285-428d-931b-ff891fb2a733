package backend

import (
	"fmt"
	"os"
	"path/filepath"
)

var AppName = "MyApp"
var InstallDir = filepath.Join(os.<PERSON><PERSON>v("LOCALAPPDATA"), AppName)
var UninstallArg = "--uninstall"

func InstallApp() (string, error) {
	if err := os.MkdirAll(InstallDir, 0755); err != nil {
		return "", err
	}

	exePath, _ := os.Executable()
	dstExe := filepath.Join(InstallDir, filepath.Base(exePath))
	if err := CopyFile(exePath, dstExe); err != nil {
		return "", err
	}

	// 创建快捷方式
	lnkPath := filepath.Join(InstallDir, "卸载 "+AppName+".lnk")
	if err := CreateShortcut(lnkPath, dstExe, UninstallArg, InstallDir); err != nil {
		return "", err
	}

	// 写注册表卸载信息
	uninstallCmd := fmt.Sprintf("\"%s\" %s", dstExe, UninstallArg)
	if err := AddUninstallInfo(AppName, InstallDir, uninstallCmd); err != nil {
		return "", err
	}

	return "安装完成！", nil
}
