package backend

import (
	"github.com/go-ole/go-ole"
	"github.com/go-ole/go-ole/oleutil"
)

func CreateShortcut(lnkPath, targetPath, args, workDir string) error {
	ole.CoInitialize(0)
	defer ole.CoUninitialize()

	ws, err := oleutil.CreateObject("WScript.Shell")
	if err != nil {
		return err
	}
	defer ws.Release()

	wshell, err := ws.QueryInterface(ole.IID_IDispatch)
	if err != nil {
		return err
	}
	defer wshell.Release()

	shortcut, err := oleutil.CallMethod(wshell, "CreateShortcut", lnkPath)
	if err != nil {
		return err
	}

	sc := shortcut.ToIDispatch()
	defer sc.Release()

	oleutil.PutProperty(sc, "TargetPath", targetPath)
	oleutil.PutProperty(sc, "Arguments", args)
	oleutil.PutProperty(sc, "WorkingDirectory", workDir)
	oleutil.PutProperty(sc, "WindowStyle", 1)
	oleutil.PutProperty(sc, "Description", "卸载 "+AppName)
	oleutil.CallMethod(sc, "Save")

	return nil
}
