import { useState } from 'react';
import logo from './assets/images/logo-universal.png';
import './App.css';
import { Greet } from "../wailsjs/go/main/App";

function App() {
    const [resultText, setResultText] = useState("Please enter your name below 👇");
    const [_, setName] = useState('');
    const updateName = (e: any) => {
        setName(e.target.value)
        Greet(e.target.value).then(updateResultText);
    };
    const updateResultText = (result: string) => setResultText(result);

    return (
        <div id="App">
            <div className='tabar'></div>
            <img src={logo} id="logo" alt="logo" />
            <div id="result" className="result">{resultText}</div>
            <div id="input" className="input-box">
                <input id="name" className="input" onChange={updateName} autoComplete="off" name="input" type="text" />
            </div>
        </div>
    )
}

export default App
